"""
AI小说管理工具 - 统一API接口
为AI Agent提供MCP兼容的统一接口
"""

import logging
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

from ..core.database import DatabaseManager
from ..managers import (
    CharacterManager, RelationshipManager, PlotManager,
    WorldBuildingManager, NotesManager
)
from ..utils.network_analysis import NetworkAnalyzer
from ..utils.timeline_analysis import TimelineAnalyzer
from ..core.exceptions import NovelManagementError
from ..config import get_config

class NovelManagementAPI:
    """小说管理系统统一API接口"""
    
    def __init__(self, db_path: str = None):
        """初始化API接口"""
        self.logger = logging.getLogger(__name__)
        
        # 初始化数据库管理器
        self.db = DatabaseManager(db_path)
        
        # 初始化各个管理器
        self.character_manager = CharacterManager(self.db)
        self.relationship_manager = RelationshipManager(self.db)
        self.plot_manager = PlotManager(self.db)
        self.worldbuilding_manager = WorldBuildingManager(self.db)
        self.notes_manager = NotesManager(self.db)
        
        # 初始化分析工具
        self.network_analyzer = NetworkAnalyzer()
        self.timeline_analyzer = TimelineAnalyzer()
        
        self.logger.info("Novel Management API initialized")
    
    # ==================== 人物管理接口 ====================
    
    def create_character(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物"""
        try:
            character_id = self.character_manager.create_character(**data)
            return {
                "success": True,
                "character_id": character_id,
                "message": f"Character '{data.get('name')}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_character", e)
    
    def get_character(self, character_id: str) -> Dict[str, Any]:
        """获取人物信息"""
        try:
            character = self.character_manager.get_character(character_id)
            if character:
                return {
                    "success": True,
                    "character": character.to_dict()
                }
            else:
                return {
                    "success": False,
                    "error": "Character not found"
                }
        except Exception as e:
            return self._handle_error("get_character", e)
    
    def update_character(self, character_id: str, updates: Dict[str, Any]) -> Dict[str, Any]:
        """更新人物信息"""
        try:
            success = self.character_manager.update_character(character_id, updates)
            return {
                "success": success,
                "message": "Character updated successfully" if success else "Update failed"
            }
        except Exception as e:
            return self._handle_error("update_character", e)
    
    def search_characters(self, criteria: Dict[str, Any] = None, limit: int = 100) -> Dict[str, Any]:
        """搜索人物"""
        try:
            results = self.character_manager.search_characters(criteria, limit)
            return {
                "success": True,
                "characters": results,
                "count": len(results)
            }
        except Exception as e:
            return self._handle_error("search_characters", e)
    
    def get_character_statistics(self) -> Dict[str, Any]:
        """获取人物统计信息"""
        try:
            stats = self.character_manager.get_character_statistics()
            return {
                "success": True,
                "statistics": stats
            }
        except Exception as e:
            return self._handle_error("get_character_statistics", e)
    
    # ==================== 关系管理接口 ====================
    
    def create_relationship(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建人物关系"""
        try:
            relationship_id = self.relationship_manager.create_relationship(**data)
            return {
                "success": True,
                "relationship_id": relationship_id,
                "message": "Relationship created successfully"
            }
        except Exception as e:
            return self._handle_error("create_relationship", e)
    
    def get_relationship_network(self, character_id: str, max_depth: int = 2) -> Dict[str, Any]:
        """获取关系网络"""
        try:
            network = self.relationship_manager.get_relationship_network(character_id, max_depth)
            analysis = self.network_analyzer.analyze_network_structure(network)
            
            return {
                "success": True,
                "network": network,
                "analysis": analysis,
                "summary": self.network_analyzer.generate_network_summary(network)
            }
        except Exception as e:
            return self._handle_error("get_relationship_network", e)
    
    def find_relationship_path(self, start_character: str, end_character: str) -> Dict[str, Any]:
        """查找关系路径"""
        try:
            path = self.relationship_manager.find_relationship_path(start_character, end_character)
            return {
                "success": True,
                "path": path,
                "path_length": len(path),
                "exists": len(path) > 0
            }
        except Exception as e:
            return self._handle_error("find_relationship_path", e)
    
    # ==================== 情节管理接口 ====================
    
    def create_event(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建事件"""
        try:
            event_id = self.plot_manager.create_event(**data)
            return {
                "success": True,
                "event_id": event_id,
                "message": f"Event '{data.get('title')}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_event", e)
    
    def get_timeline(self, filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """获取时间线"""
        try:
            timeline_events = self.plot_manager.get_timeline(**filters if filters else {})
            timeline_data = self.timeline_analyzer.create_timeline_visualization(timeline_events)
            
            return {
                "success": True,
                "timeline": timeline_data,
                "summary": self.timeline_analyzer.generate_timeline_summary(timeline_data)
            }
        except Exception as e:
            return self._handle_error("get_timeline", e)
    
    def analyze_plot_structure(self) -> Dict[str, Any]:
        """分析情节结构"""
        try:
            analysis = self.plot_manager.analyze_plot_structure()
            return {
                "success": True,
                "analysis": analysis
            }
        except Exception as e:
            return self._handle_error("analyze_plot_structure", e)
    
    def add_causal_relationship(self, cause_event_id: str, effect_event_id: str) -> Dict[str, Any]:
        """添加因果关系"""
        try:
            success = self.plot_manager.add_causal_relationship(cause_event_id, effect_event_id)
            return {
                "success": success,
                "message": "Causal relationship added successfully" if success else "Failed to add causal relationship"
            }
        except Exception as e:
            return self._handle_error("add_causal_relationship", e)
    
    # ==================== 世界观管理接口 ====================
    
    def create_world_setting(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建世界观设定"""
        try:
            setting_id = self.worldbuilding_manager.create_setting(**data)
            return {
                "success": True,
                "setting_id": setting_id,
                "message": f"World setting '{data.get('name')}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_world_setting", e)
    
    def check_world_consistency(self) -> Dict[str, Any]:
        """检查世界观一致性"""
        try:
            issues = self.worldbuilding_manager.check_consistency()
            return {
                "success": True,
                "consistency_issues": issues,
                "issue_count": len(issues),
                "has_issues": len(issues) > 0
            }
        except Exception as e:
            return self._handle_error("check_world_consistency", e)
    
    def get_setting_network(self, setting_id: str, max_depth: int = 3) -> Dict[str, Any]:
        """获取设定关联网络"""
        try:
            network = self.worldbuilding_manager.get_setting_network(setting_id, max_depth)
            return {
                "success": True,
                "network": network
            }
        except Exception as e:
            return self._handle_error("get_setting_network", e)
    
    # ==================== 创作笔记接口 ====================
    
    def create_note(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """创建创作笔记"""
        try:
            note_id = self.notes_manager.create_note(**data)
            return {
                "success": True,
                "note_id": note_id,
                "message": f"Note '{data.get('title')}' created successfully"
            }
        except Exception as e:
            return self._handle_error("create_note", e)
    
    def search_notes_by_content(self, query: str, limit: int = 50) -> Dict[str, Any]:
        """根据内容搜索笔记"""
        try:
            results = self.notes_manager.search_notes_by_content(query, limit)
            return {
                "success": True,
                "notes": results,
                "count": len(results),
                "query": query
            }
        except Exception as e:
            return self._handle_error("search_notes_by_content", e)
    
    def get_related_notes(self, entity_type: str, entity_id: str) -> Dict[str, Any]:
        """获取相关笔记"""
        try:
            notes = self.notes_manager.get_related_notes(entity_type, entity_id)
            return {
                "success": True,
                "notes": notes,
                "count": len(notes)
            }
        except Exception as e:
            return self._handle_error("get_related_notes", e)
    
    # ==================== 智能查询接口 ====================
    
    def intelligent_query(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """智能查询接口"""
        try:
            # 解析查询意图
            intent = self._parse_query_intent(query)
            
            # 根据意图执行相应操作
            if intent["type"] == "character_search":
                return self._handle_character_query(intent, context)
            elif intent["type"] == "relationship_analysis":
                return self._handle_relationship_query(intent, context)
            elif intent["type"] == "plot_analysis":
                return self._handle_plot_query(intent, context)
            elif intent["type"] == "world_query":
                return self._handle_world_query(intent, context)
            elif intent["type"] == "note_search":
                return self._handle_note_query(intent, context)
            else:
                return self._handle_general_query(query, context)
                
        except Exception as e:
            return self._handle_error("intelligent_query", e)
    
    def get_system_overview(self) -> Dict[str, Any]:
        """获取系统概览"""
        try:
            overview = {
                "characters": self.character_manager.get_character_statistics(),
                "relationships": self.relationship_manager.get_relationship_statistics(),
                "events": self.plot_manager._calculate_plot_metrics(),
                "world_settings": self.worldbuilding_manager.get_setting_statistics(),
                "notes": self.notes_manager.get_note_statistics()
            }
            
            return {
                "success": True,
                "overview": overview,
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return self._handle_error("get_system_overview", e)
    
    def export_data(self, data_type: str, format_type: str = "json", filters: Dict[str, Any] = None) -> Dict[str, Any]:
        """导出数据"""
        try:
            if data_type == "characters":
                data = self.character_manager.search_characters(filters)
            elif data_type == "relationships":
                # 获取所有关系数据
                data = self.relationship_manager.db.search_entities("relationships", filters or {"status": "active"})
            elif data_type == "events":
                data = self.plot_manager.search_events(filters)
            elif data_type == "world_settings":
                data = self.worldbuilding_manager.search_settings(filters)
            elif data_type == "notes":
                data = self.notes_manager.search_notes(filters)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported data type: {data_type}"
                }
            
            # 格式化导出数据
            if format_type == "json":
                exported_data = json.dumps(data, ensure_ascii=False, indent=2, default=str)
            elif format_type == "csv":
                exported_data = self._convert_to_csv(data)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported format: {format_type}"
                }
            
            return {
                "success": True,
                "data": exported_data,
                "format": format_type,
                "count": len(data)
            }
            
        except Exception as e:
            return self._handle_error("export_data", e)
    
    # ==================== 私有辅助方法 ====================
    
    def _handle_error(self, operation: str, error: Exception) -> Dict[str, Any]:
        """统一错误处理"""
        error_msg = str(error)
        self.logger.error(f"Error in {operation}: {error_msg}")
        
        return {
            "success": False,
            "error": error_msg,
            "operation": operation,
            "error_type": type(error).__name__
        }
    
    def _parse_query_intent(self, query: str) -> Dict[str, Any]:
        """解析查询意图"""
        query_lower = query.lower()
        
        # 人物相关查询
        if any(keyword in query_lower for keyword in ["character", "人物", "角色", "who is", "谁是"]):
            return {"type": "character_search", "query": query}
        
        # 关系相关查询
        elif any(keyword in query_lower for keyword in ["relationship", "关系", "connection", "连接"]):
            return {"type": "relationship_analysis", "query": query}
        
        # 情节相关查询
        elif any(keyword in query_lower for keyword in ["plot", "story", "event", "情节", "故事", "事件"]):
            return {"type": "plot_analysis", "query": query}
        
        # 世界观相关查询
        elif any(keyword in query_lower for keyword in ["world", "setting", "世界", "设定", "背景"]):
            return {"type": "world_query", "query": query}
        
        # 笔记相关查询
        elif any(keyword in query_lower for keyword in ["note", "idea", "笔记", "想法", "灵感"]):
            return {"type": "note_search", "query": query}
        
        else:
            return {"type": "general", "query": query}
    
    def _handle_character_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理人物查询"""
        query = intent["query"]
        
        # 简单的关键词提取
        keywords = query.split()
        
        # 搜索人物
        results = self.character_manager.search_characters(limit=10)
        
        # 过滤相关结果
        relevant_results = []
        for char in results:
            char_text = f"{char.get('name', '')} {char.get('background', '')}".lower()
            if any(keyword.lower() in char_text for keyword in keywords):
                relevant_results.append(char)
        
        return {
            "success": True,
            "type": "character_search",
            "results": relevant_results[:5],
            "count": len(relevant_results)
        }
    
    def _handle_relationship_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理关系查询"""
        # 获取关系统计
        stats = self.relationship_manager.get_relationship_statistics()
        
        return {
            "success": True,
            "type": "relationship_analysis",
            "statistics": stats
        }
    
    def _handle_plot_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理情节查询"""
        # 获取情节分析
        analysis = self.plot_manager.analyze_plot_structure()
        
        return {
            "success": True,
            "type": "plot_analysis",
            "analysis": analysis
        }
    
    def _handle_world_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理世界观查询"""
        # 获取世界观统计
        stats = self.worldbuilding_manager.get_setting_statistics()
        
        return {
            "success": True,
            "type": "world_query",
            "statistics": stats
        }
    
    def _handle_note_query(self, intent: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """处理笔记查询"""
        query = intent["query"]
        
        # 搜索笔记
        results = self.notes_manager.search_notes_by_content(query)
        
        return {
            "success": True,
            "type": "note_search",
            "results": results[:10],
            "count": len(results)
        }
    
    def _handle_general_query(self, query: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """处理通用查询"""
        # 获取系统概览
        overview = self.get_system_overview()
        
        return {
            "success": True,
            "type": "general",
            "message": "Here's an overview of your novel management system",
            "overview": overview.get("overview", {})
        }
    
    def _convert_to_csv(self, data: List[Dict[str, Any]]) -> str:
        """转换为CSV格式"""
        if not data:
            return ""
        
        # 获取所有字段
        all_fields = set()
        for item in data:
            all_fields.update(item.keys())
        
        fields = sorted(all_fields)
        
        # 生成CSV
        csv_lines = [",".join(fields)]
        
        for item in data:
            row = []
            for field in fields:
                value = item.get(field, "")
                if isinstance(value, (list, dict)):
                    value = json.dumps(value, ensure_ascii=False)
                row.append(f'"{str(value)}"')
            csv_lines.append(",".join(row))
        
        return "\n".join(csv_lines)
