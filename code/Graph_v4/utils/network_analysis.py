"""
AI小说管理工具 - 网络分析工具
提供关系网络的分析和可视化功能
"""

import json
from typing import Dict, List, Any, Tuple, Set, Optional
from collections import defaultdict, Counter
import math

class NetworkAnalyzer:
    """网络分析器"""
    
    def __init__(self):
        """初始化网络分析器"""
        pass
    
    def analyze_network_structure(self, network: Dict[str, Any]) -> Dict[str, Any]:
        """分析网络结构"""
        nodes = network.get("nodes", {})
        edges = network.get("edges", [])
        
        analysis = {
            "basic_metrics": self._calculate_basic_metrics(nodes, edges),
            "centrality_metrics": self._calculate_centrality_metrics(nodes, edges),
            "community_structure": self._detect_communities(nodes, edges),
            "relationship_patterns": self._analyze_relationship_patterns(edges),
            "network_density": self._calculate_network_density(nodes, edges)
        }
        
        return analysis
    
    def _calculate_basic_metrics(self, nodes: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算基本网络指标"""
        num_nodes = len(nodes)
        num_edges = len(edges)
        
        # 计算度分布
        degree_count = defaultdict(int)
        for edge in edges:
            degree_count[edge["source"]] += 1
            degree_count[edge["target"]] += 1
        
        degrees = list(degree_count.values())
        avg_degree = sum(degrees) / len(degrees) if degrees else 0
        max_degree = max(degrees) if degrees else 0
        
        return {
            "node_count": num_nodes,
            "edge_count": num_edges,
            "average_degree": round(avg_degree, 2),
            "max_degree": max_degree,
            "degree_distribution": dict(Counter(degrees))
        }
    
    def _calculate_centrality_metrics(self, nodes: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """计算中心性指标"""
        # 构建邻接表
        adjacency = defaultdict(set)
        for edge in edges:
            adjacency[edge["source"]].add(edge["target"])
            adjacency[edge["target"]].add(edge["source"])
        
        # 度中心性
        degree_centrality = {}
        max_degree = 0
        for node_id in nodes:
            degree = len(adjacency[node_id])
            degree_centrality[node_id] = degree
            max_degree = max(max_degree, degree)
        
        # 标准化度中心性
        if max_degree > 0:
            for node_id in degree_centrality:
                degree_centrality[node_id] = degree_centrality[node_id] / max_degree
        
        # 接近中心性（简化版本）
        closeness_centrality = self._calculate_closeness_centrality(nodes, adjacency)
        
        # 中介中心性（简化版本）
        betweenness_centrality = self._calculate_betweenness_centrality(nodes, adjacency)
        
        return {
            "degree_centrality": degree_centrality,
            "closeness_centrality": closeness_centrality,
            "betweenness_centrality": betweenness_centrality,
            "most_central_nodes": self._get_top_central_nodes(degree_centrality, 5)
        }
    
    def _calculate_closeness_centrality(self, nodes: Dict[str, Any], adjacency: Dict[str, Set[str]]) -> Dict[str, float]:
        """计算接近中心性"""
        closeness = {}
        
        for node_id in nodes:
            # 使用BFS计算到所有其他节点的最短距离
            distances = self._bfs_distances(node_id, adjacency)
            
            if len(distances) > 1:
                total_distance = sum(distances.values())
                # 接近中心性 = (n-1) / sum(distances)
                closeness[node_id] = (len(distances) - 1) / total_distance if total_distance > 0 else 0
            else:
                closeness[node_id] = 0
        
        return closeness
    
    def _calculate_betweenness_centrality(self, nodes: Dict[str, Any], adjacency: Dict[str, Set[str]]) -> Dict[str, float]:
        """计算中介中心性（简化版本）"""
        betweenness = {node_id: 0.0 for node_id in nodes}
        
        # 对于每对节点，计算通过每个节点的最短路径数量
        node_list = list(nodes.keys())
        for i, source in enumerate(node_list):
            for target in node_list[i+1:]:
                if source != target:
                    paths = self._find_shortest_paths(source, target, adjacency)
                    if len(paths) > 0:
                        for path in paths:
                            # 为路径上的中间节点增加中介性分数
                            for node in path[1:-1]:  # 排除起点和终点
                                betweenness[node] += 1.0 / len(paths)
        
        # 标准化
        n = len(nodes)
        if n > 2:
            normalization = 2.0 / ((n - 1) * (n - 2))
            for node_id in betweenness:
                betweenness[node_id] *= normalization
        
        return betweenness
    
    def _bfs_distances(self, start_node: str, adjacency: Dict[str, Set[str]]) -> Dict[str, int]:
        """使用BFS计算从起始节点到所有其他节点的距离"""
        distances = {start_node: 0}
        queue = [start_node]
        
        while queue:
            current = queue.pop(0)
            current_distance = distances[current]
            
            for neighbor in adjacency[current]:
                if neighbor not in distances:
                    distances[neighbor] = current_distance + 1
                    queue.append(neighbor)
        
        return distances
    
    def _find_shortest_paths(self, source: str, target: str, adjacency: Dict[str, Set[str]]) -> List[List[str]]:
        """查找两个节点之间的所有最短路径"""
        if source == target:
            return [[source]]
        
        # BFS查找最短路径
        queue = [[source]]
        visited = {source}
        paths = []
        min_length = float('inf')
        
        while queue:
            path = queue.pop(0)
            current = path[-1]
            
            if len(path) > min_length:
                break
            
            for neighbor in adjacency[current]:
                new_path = path + [neighbor]
                
                if neighbor == target:
                    if len(new_path) < min_length:
                        min_length = len(new_path)
                        paths = [new_path]
                    elif len(new_path) == min_length:
                        paths.append(new_path)
                elif neighbor not in visited or len(new_path) <= min_length:
                    queue.append(new_path)
                    visited.add(neighbor)
        
        return paths
    
    def _detect_communities(self, nodes: Dict[str, Any], edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """检测社区结构（简化版本）"""
        # 构建邻接表
        adjacency = defaultdict(set)
        for edge in edges:
            adjacency[edge["source"]].add(edge["target"])
            adjacency[edge["target"]].add(edge["source"])
        
        # 使用简单的连通分量检测
        communities = []
        visited = set()
        
        for node_id in nodes:
            if node_id not in visited:
                community = self._dfs_component(node_id, adjacency, visited)
                if community:
                    communities.append(community)
        
        return {
            "community_count": len(communities),
            "communities": communities,
            "modularity": self._calculate_modularity(communities, edges)
        }
    
    def _dfs_component(self, start_node: str, adjacency: Dict[str, Set[str]], visited: Set[str]) -> List[str]:
        """使用DFS查找连通分量"""
        component = []
        stack = [start_node]
        
        while stack:
            node = stack.pop()
            if node not in visited:
                visited.add(node)
                component.append(node)
                
                for neighbor in adjacency[node]:
                    if neighbor not in visited:
                        stack.append(neighbor)
        
        return component
    
    def _calculate_modularity(self, communities: List[List[str]], edges: List[Dict[str, Any]]) -> float:
        """计算模块度（简化版本）"""
        if not communities or not edges:
            return 0.0
        
        # 创建节点到社区的映射
        node_to_community = {}
        for i, community in enumerate(communities):
            for node in community:
                node_to_community[node] = i
        
        total_edges = len(edges)
        if total_edges == 0:
            return 0.0
        
        # 计算模块度
        modularity = 0.0
        for edge in edges:
            source_community = node_to_community.get(edge["source"])
            target_community = node_to_community.get(edge["target"])
            
            if source_community is not None and target_community is not None:
                if source_community == target_community:
                    modularity += 1.0
        
        return modularity / total_edges
    
    def _analyze_relationship_patterns(self, edges: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析关系模式"""
        type_counts = Counter(edge["type"] for edge in edges)
        strength_distribution = [edge["strength"] for edge in edges if "strength" in edge]
        
        # 计算强度统计
        if strength_distribution:
            avg_strength = sum(strength_distribution) / len(strength_distribution)
            max_strength = max(strength_distribution)
            min_strength = min(strength_distribution)
        else:
            avg_strength = max_strength = min_strength = 0
        
        return {
            "relationship_types": dict(type_counts),
            "strength_stats": {
                "average": round(avg_strength, 3),
                "maximum": max_strength,
                "minimum": min_strength,
                "count": len(strength_distribution)
            },
            "mutual_relationships": sum(1 for edge in edges if edge.get("is_mutual", False))
        }
    
    def _calculate_network_density(self, nodes: Dict[str, Any], edges: List[Dict[str, Any]]) -> float:
        """计算网络密度"""
        n = len(nodes)
        if n < 2:
            return 0.0
        
        max_possible_edges = n * (n - 1) / 2  # 无向图的最大边数
        actual_edges = len(edges)
        
        return actual_edges / max_possible_edges if max_possible_edges > 0 else 0.0
    
    def _get_top_central_nodes(self, centrality_scores: Dict[str, float], top_k: int = 5) -> List[Dict[str, Any]]:
        """获取中心性最高的节点"""
        sorted_nodes = sorted(centrality_scores.items(), key=lambda x: x[1], reverse=True)
        return [
            {"node_id": node_id, "centrality_score": round(score, 3)}
            for node_id, score in sorted_nodes[:top_k]
        ]
    
    def generate_network_summary(self, network: Dict[str, Any]) -> str:
        """生成网络摘要"""
        analysis = self.analyze_network_structure(network)
        
        basic = analysis["basic_metrics"]
        centrality = analysis["centrality_metrics"]
        community = analysis["community_structure"]
        patterns = analysis["relationship_patterns"]
        
        summary_parts = [
            f"网络包含 {basic['node_count']} 个节点和 {basic['edge_count']} 条边",
            f"平均度数: {basic['average_degree']}",
            f"网络密度: {analysis['network_density']:.3f}",
            f"检测到 {community['community_count']} 个社区",
            f"最常见的关系类型: {max(patterns['relationship_types'].items(), key=lambda x: x[1])[0] if patterns['relationship_types'] else '无'}",
            f"平均关系强度: {patterns['strength_stats']['average']}"
        ]
        
        if centrality["most_central_nodes"]:
            most_central = centrality["most_central_nodes"][0]
            summary_parts.append(f"最中心的节点: {most_central['node_id']} (中心性: {most_central['centrality_score']})")
        
        return "；".join(summary_parts)
    
    def export_network_for_visualization(self, network: Dict[str, Any], format_type: str = "json") -> str:
        """导出网络数据用于可视化"""
        if format_type == "json":
            return json.dumps(network, ensure_ascii=False, indent=2)
        elif format_type == "graphml":
            return self._export_to_graphml(network)
        elif format_type == "gexf":
            return self._export_to_gexf(network)
        else:
            raise ValueError(f"Unsupported format: {format_type}")
    
    def _export_to_graphml(self, network: Dict[str, Any]) -> str:
        """导出为GraphML格式"""
        nodes = network.get("nodes", {})
        edges = network.get("edges", [])
        
        graphml_parts = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<graphml xmlns="http://graphml.graphdrawing.org/xmlns">',
            '  <key id="name" for="node" attr.name="name" attr.type="string"/>',
            '  <key id="importance" for="node" attr.name="importance" attr.type="int"/>',
            '  <key id="type" for="edge" attr.name="type" attr.type="string"/>',
            '  <key id="strength" for="edge" attr.name="strength" attr.type="double"/>',
            '  <graph id="G" edgedefault="undirected">'
        ]
        
        # 添加节点
        for node_id, node_data in nodes.items():
            graphml_parts.append(f'    <node id="{node_id}">')
            graphml_parts.append(f'      <data key="name">{node_data.get("name", "")}</data>')
            graphml_parts.append(f'      <data key="importance">{node_data.get("importance", 5)}</data>')
            graphml_parts.append('    </node>')
        
        # 添加边
        for i, edge in enumerate(edges):
            graphml_parts.append(f'    <edge id="e{i}" source="{edge["source"]}" target="{edge["target"]}">')
            graphml_parts.append(f'      <data key="type">{edge.get("type", "")}</data>')
            graphml_parts.append(f'      <data key="strength">{edge.get("strength", 0.5)}</data>')
            graphml_parts.append('    </edge>')
        
        graphml_parts.extend(['  </graph>', '</graphml>'])
        
        return '\n'.join(graphml_parts)
    
    def _export_to_gexf(self, network: Dict[str, Any]) -> str:
        """导出为GEXF格式"""
        nodes = network.get("nodes", {})
        edges = network.get("edges", [])
        
        gexf_parts = [
            '<?xml version="1.0" encoding="UTF-8"?>',
            '<gexf xmlns="http://www.gexf.net/1.2draft" version="1.2">',
            '  <graph mode="static" defaultedgetype="undirected">',
            '    <attributes class="node">',
            '      <attribute id="0" title="name" type="string"/>',
            '      <attribute id="1" title="importance" type="integer"/>',
            '    </attributes>',
            '    <attributes class="edge">',
            '      <attribute id="0" title="type" type="string"/>',
            '      <attribute id="1" title="strength" type="double"/>',
            '    </attributes>',
            '    <nodes>'
        ]
        
        # 添加节点
        for node_id, node_data in nodes.items():
            gexf_parts.append(f'      <node id="{node_id}" label="{node_data.get("name", "")}">')
            gexf_parts.append('        <attvalues>')
            gexf_parts.append(f'          <attvalue for="0" value="{node_data.get("name", "")}"/>')
            gexf_parts.append(f'          <attvalue for="1" value="{node_data.get("importance", 5)}"/>')
            gexf_parts.append('        </attvalues>')
            gexf_parts.append('      </node>')
        
        gexf_parts.append('    </nodes>')
        gexf_parts.append('    <edges>')
        
        # 添加边
        for i, edge in enumerate(edges):
            gexf_parts.append(f'      <edge id="{i}" source="{edge["source"]}" target="{edge["target"]}">')
            gexf_parts.append('        <attvalues>')
            gexf_parts.append(f'          <attvalue for="0" value="{edge.get("type", "")}"/>')
            gexf_parts.append(f'          <attvalue for="1" value="{edge.get("strength", 0.5)}"/>')
            gexf_parts.append('        </attvalues>')
            gexf_parts.append('      </edge>')
        
        gexf_parts.extend(['    </edges>', '  </graph>', '</gexf>'])
        
        return '\n'.join(gexf_parts)
