"""
AI小说管理工具 - 人物管理模块
提供人物档案的创建、查询、更新和分析功能
"""

import logging
from datetime import datetime
from typing import List, Dict, Any, Optional, Union
from dataclasses import asdict

from ..core.database import DatabaseManager
from ..core.models import Character, PersonalityTrait, DevelopmentStage, EntityStatus
from ..core.exceptions import (
    ValidationError, EntityNotFoundError, DuplicateEntityError, DatabaseError
)
from ..config import get_config

class CharacterManager:
    """人物管理器"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """初始化人物管理器"""
        self.db = db_manager or DatabaseManager()
        self.logger = logging.getLogger(__name__)
        self.validation_config = get_config("validation")
    
    def create_character(self, name: str, **kwargs) -> str:
        """创建新人物"""
        try:
            # 验证必要字段
            if not name or len(name.strip()) == 0:
                raise ValidationError("Character name cannot be empty", "name")
            
            if len(name) > self.validation_config["max_name_length"]:
                raise ValidationError(
                    f"Character name too long (max {self.validation_config['max_name_length']} chars)",
                    "name", name
                )
            
            # 检查是否已存在同名人物
            existing = self.search_characters({"name": name.strip()})
            if existing:
                raise DuplicateEntityError("Character", name)
            
            # 创建人物对象
            character = Character(name=name.strip(), **kwargs)
            
            # 验证其他字段
            self._validate_character(character)
            
            # 保存到数据库
            character_id = self.db.insert_entity("characters", character)
            
            self.logger.info(f"Created character: {name} (ID: {character_id})")
            return character_id
            
        except Exception as e:
            self.logger.error(f"Failed to create character {name}: {str(e)}")
            raise
    
    def get_character(self, character_id: str) -> Optional[Character]:
        """根据ID获取人物"""
        try:
            data = self.db.get_entity("characters", character_id)
            if not data:
                return None
            
            # 反序列化JSON字段
            data = self._deserialize_character_data(data)
            
            return Character.from_dict(data)
            
        except Exception as e:
            self.logger.error(f"Failed to get character {character_id}: {str(e)}")
            raise DatabaseError(f"Failed to retrieve character: {str(e)}")
    
    def update_character(self, character_id: str, updates: Dict[str, Any]) -> bool:
        """更新人物信息"""
        try:
            # 检查人物是否存在
            existing = self.get_character(character_id)
            if not existing:
                raise EntityNotFoundError("Character", character_id)
            
            # 验证更新数据
            if "name" in updates:
                if not updates["name"] or len(updates["name"].strip()) == 0:
                    raise ValidationError("Character name cannot be empty", "name")
                
                # 检查名称冲突（排除自己）
                existing_with_name = self.search_characters({"name": updates["name"].strip()})
                if existing_with_name and existing_with_name[0]["id"] != character_id:
                    raise DuplicateEntityError("Character", updates["name"])
            
            # 序列化复杂字段
            serialized_updates = self._serialize_character_updates(updates)
            
            # 更新数据库
            success = self.db.update_entity("characters", character_id, serialized_updates)
            
            if success:
                self.logger.info(f"Updated character {character_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to update character {character_id}: {str(e)}")
            raise
    
    def delete_character(self, character_id: str) -> bool:
        """删除人物（软删除）"""
        try:
            # 检查人物是否存在
            existing = self.get_character(character_id)
            if not existing:
                raise EntityNotFoundError("Character", character_id)
            
            # 执行软删除
            success = self.db.delete_entity("characters", character_id)
            
            if success:
                self.logger.info(f"Deleted character {character_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to delete character {character_id}: {str(e)}")
            raise
    
    def search_characters(self, criteria: Dict[str, Any] = None, 
                         limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索人物"""
        try:
            # 默认只查询活跃状态的人物
            search_criteria = {"status": "active"}
            if criteria:
                search_criteria.update(criteria)
            
            results = self.db.search_entities("characters", search_criteria, limit, offset)
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_character_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to search characters: {str(e)}")
            raise DatabaseError(f"Character search failed: {str(e)}")
    
    def get_characters_by_importance(self, min_importance: int = 1, 
                                   max_importance: int = 10) -> List[Dict[str, Any]]:
        """根据重要性获取人物"""
        sql = """
            SELECT * FROM characters 
            WHERE status = 'active' AND importance BETWEEN ? AND ?
            ORDER BY importance DESC, name ASC
        """
        
        try:
            results = self.db.execute_custom_query(sql, (min_importance, max_importance))
            
            # 反序列化结果
            for result in results:
                result.update(self._deserialize_character_data(result))
            
            return results
            
        except Exception as e:
            self.logger.error(f"Failed to get characters by importance: {str(e)}")
            raise DatabaseError(f"Failed to get characters by importance: {str(e)}")
    
    def add_personality_trait(self, character_id: str, trait: str, 
                            description: str = "", intensity: float = 0.5) -> bool:
        """为人物添加性格特征"""
        try:
            character = self.get_character(character_id)
            if not character:
                raise EntityNotFoundError("Character", character_id)
            
            # 创建新的性格特征
            new_trait = PersonalityTrait(trait=trait, description=description, intensity=intensity)
            
            # 检查是否已存在相同特征
            existing_traits = [t.trait for t in character.personality_traits]
            if trait in existing_traits:
                raise ValidationError(f"Personality trait '{trait}' already exists", "trait")
            
            # 添加特征
            character.personality_traits.append(new_trait)
            
            # 更新数据库
            updates = {"personality_traits": [asdict(t) for t in character.personality_traits]}
            return self.update_character(character_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to add personality trait: {str(e)}")
            raise
    
    def add_development_stage(self, character_id: str, stage: str, 
                            description: str, key_events: List[str] = None,
                            timestamp: datetime = None) -> bool:
        """为人物添加发展阶段"""
        try:
            character = self.get_character(character_id)
            if not character:
                raise EntityNotFoundError("Character", character_id)
            
            # 创建新的发展阶段
            new_stage = DevelopmentStage(
                stage=stage,
                description=description,
                key_events=key_events or [],
                timestamp=timestamp or datetime.now()
            )
            
            # 添加发展阶段
            character.development_arc.append(new_stage)
            
            # 更新数据库
            updates = {"development_arc": [asdict(s) for s in character.development_arc]}
            return self.update_character(character_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to add development stage: {str(e)}")
            raise
    
    def get_character_statistics(self) -> Dict[str, Any]:
        """获取人物统计信息"""
        try:
            stats = {}
            
            # 总人物数
            total_sql = "SELECT COUNT(*) as count FROM characters WHERE status = 'active'"
            total_result = self.db.execute_custom_query(total_sql)
            stats["total_characters"] = total_result[0]["count"] if total_result else 0
            
            # 按重要性分布
            importance_sql = """
                SELECT importance, COUNT(*) as count 
                FROM characters 
                WHERE status = 'active' 
                GROUP BY importance 
                ORDER BY importance DESC
            """
            importance_result = self.db.execute_custom_query(importance_sql)
            stats["importance_distribution"] = {
                str(row["importance"]): row["count"] for row in importance_result
            }
            
            # 按性别分布
            gender_sql = """
                SELECT gender, COUNT(*) as count 
                FROM characters 
                WHERE status = 'active' AND gender != '' 
                GROUP BY gender
            """
            gender_result = self.db.execute_custom_query(gender_sql)
            stats["gender_distribution"] = {
                row["gender"]: row["count"] for row in gender_result
            }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get character statistics: {str(e)}")
            raise DatabaseError(f"Failed to get statistics: {str(e)}")
    
    def _validate_character(self, character: Character):
        """验证人物数据"""
        if character.importance < 1 or character.importance > 10:
            raise ValidationError("Character importance must be between 1 and 10", "importance")
        
        if character.age is not None and (character.age < 0 or character.age > 1000):
            raise ValidationError("Character age must be between 0 and 1000", "age")
        
        if len(character.background) > self.validation_config["max_description_length"]:
            raise ValidationError("Character background too long", "background")
    
    def _serialize_character_updates(self, updates: Dict[str, Any]) -> Dict[str, Any]:
        """序列化人物更新数据中的复杂字段"""
        serialized = updates.copy()
        
        # 处理需要序列化的字段
        json_fields = [
            "aliases", "personality_traits", "motivations", "fears", "goals",
            "distinctive_features", "development_arc", "tags"
        ]
        
        for field in json_fields:
            if field in serialized:
                serialized[field] = self.db._serialize_json_field(serialized[field])
        
        return serialized
    
    def _deserialize_character_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """反序列化人物数据中的JSON字段"""
        deserialized = data.copy()
        
        # 处理需要反序列化的字段
        json_fields = [
            "aliases", "personality_traits", "motivations", "fears", "goals",
            "distinctive_features", "development_arc", "tags"
        ]
        
        for field in json_fields:
            if field in deserialized and deserialized[field]:
                deserialized[field] = self.db._deserialize_json_field(deserialized[field])
        
        return deserialized
