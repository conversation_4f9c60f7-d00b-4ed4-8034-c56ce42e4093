"""
AI小说管理工具 - 数据库管理模块
提供SQLite数据库连接和基础操作功能
"""

import sqlite3
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from contextlib import contextmanager

from ..config import get_config
from .exceptions import DatabaseError, ValidationError
from .models import BaseEntity

class DatabaseManager:
    """数据库管理器"""
    
    def __init__(self, db_path: str = None):
        """初始化数据库管理器"""
        self.db_path = db_path or get_config("database")["main_db"]
        self.logger = logging.getLogger(__name__)
        self._ensure_database_exists()
        self._create_tables()
    
    def _ensure_database_exists(self):
        """确保数据库文件存在"""
        db_file = Path(self.db_path)
        db_file.parent.mkdir(parents=True, exist_ok=True)
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            conn = sqlite3.connect(self.db_path)
            conn.row_factory = sqlite3.Row  # 使结果可以按列名访问
            yield conn
        except sqlite3.Error as e:
            if conn:
                conn.rollback()
            raise DatabaseError(f"Database operation failed: {str(e)}")
        finally:
            if conn:
                conn.close()
    
    def _create_tables(self):
        """创建数据库表"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            
            # 人物表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS characters (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    aliases TEXT,  -- JSON array
                    age INTEGER,
                    gender TEXT,
                    occupation TEXT,
                    background TEXT,
                    personality_traits TEXT,  -- JSON array
                    motivations TEXT,  -- JSON array
                    fears TEXT,  -- JSON array
                    goals TEXT,  -- JSON array
                    physical_description TEXT,
                    distinctive_features TEXT,  -- JSON array
                    development_arc TEXT,  -- JSON array
                    importance INTEGER DEFAULT 5,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 关系表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS relationships (
                    id TEXT PRIMARY KEY,
                    character_a TEXT NOT NULL,
                    character_b TEXT NOT NULL,
                    relationship_type TEXT NOT NULL,
                    strength REAL DEFAULT 0.5,
                    description TEXT,
                    history TEXT,  -- JSON array
                    is_mutual BOOLEAN DEFAULT 1,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (character_a) REFERENCES characters (id),
                    FOREIGN KEY (character_b) REFERENCES characters (id)
                )
            """)
            
            # 事件表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS events (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    description TEXT,
                    event_type TEXT NOT NULL,
                    story_timestamp TEXT,
                    duration TEXT,
                    location TEXT,
                    participants TEXT,  -- JSON array
                    consequences TEXT,  -- JSON array
                    causes TEXT,  -- JSON array
                    importance INTEGER DEFAULT 5,
                    writing_status TEXT DEFAULT 'planned',
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 世界观设定表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS world_settings (
                    id TEXT PRIMARY KEY,
                    category TEXT NOT NULL,
                    name TEXT NOT NULL,
                    description TEXT,
                    rules TEXT,  -- JSON array
                    related_settings TEXT,  -- JSON array
                    consistency_notes TEXT,  -- JSON array
                    version INTEGER DEFAULT 1,
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创作笔记表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS creative_notes (
                    id TEXT PRIMARY KEY,
                    title TEXT NOT NULL,
                    content TEXT,
                    note_type TEXT NOT NULL,
                    related_characters TEXT,  -- JSON array
                    related_events TEXT,  -- JSON array
                    related_settings TEXT,  -- JSON array
                    priority INTEGER DEFAULT 3,
                    usage_status TEXT DEFAULT 'active',
                    status TEXT DEFAULT 'active',
                    tags TEXT,  -- JSON array
                    created_at TEXT,
                    updated_at TEXT
                )
            """)
            
            # 创建索引
            self._create_indexes(cursor)
            
            conn.commit()
    
    def _create_indexes(self, cursor):
        """创建数据库索引"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_characters_name ON characters(name)",
            "CREATE INDEX IF NOT EXISTS idx_characters_status ON characters(status)",
            "CREATE INDEX IF NOT EXISTS idx_relationships_characters ON relationships(character_a, character_b)",
            "CREATE INDEX IF NOT EXISTS idx_events_timestamp ON events(story_timestamp)",
            "CREATE INDEX IF NOT EXISTS idx_events_type ON events(event_type)",
            "CREATE INDEX IF NOT EXISTS idx_world_settings_category ON world_settings(category)",
            "CREATE INDEX IF NOT EXISTS idx_notes_type ON creative_notes(note_type)",
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
    
    def _serialize_json_field(self, value: Any) -> str:
        """序列化JSON字段"""
        if value is None:
            return "[]"
        if isinstance(value, (list, dict)):
            return json.dumps(value, ensure_ascii=False, default=str)
        return str(value)
    
    def _deserialize_json_field(self, value: str) -> Any:
        """反序列化JSON字段"""
        if not value:
            return []
        try:
            return json.loads(value)
        except (json.JSONDecodeError, TypeError):
            return []
    
    def insert_entity(self, table: str, entity: BaseEntity) -> str:
        """插入实体到数据库"""
        try:
            entity.updated_at = datetime.now()
            data = entity.to_dict()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建插入SQL
                columns = list(data.keys())
                placeholders = ["?" for _ in columns]
                
                sql = f"INSERT INTO {table} ({', '.join(columns)}) VALUES ({', '.join(placeholders)})"
                
                # 处理JSON字段
                values = []
                for key, value in data.items():
                    if key in ['aliases', 'personality_traits', 'motivations', 'fears', 'goals', 
                              'distinctive_features', 'development_arc', 'history', 'participants',
                              'consequences', 'causes', 'rules', 'related_settings', 
                              'consistency_notes', 'related_characters', 'related_events', 
                              'related_settings', 'tags']:
                        values.append(self._serialize_json_field(value))
                    else:
                        values.append(value)
                
                cursor.execute(sql, values)
                conn.commit()
                
                return entity.id
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to insert entity: {str(e)}", "INSERT")
    
    def get_entity(self, table: str, entity_id: str) -> Optional[Dict[str, Any]]:
        """根据ID获取实体"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(f"SELECT * FROM {table} WHERE id = ?", (entity_id,))
                row = cursor.fetchone()
                
                if row:
                    return dict(row)
                return None
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to get entity: {str(e)}", "SELECT")
    
    def update_entity(self, table: str, entity_id: str, updates: Dict[str, Any]) -> bool:
        """更新实体"""
        try:
            updates['updated_at'] = datetime.now().isoformat()
            
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                # 构建更新SQL
                set_clauses = []
                values = []
                
                for key, value in updates.items():
                    set_clauses.append(f"{key} = ?")
                    if key in ['aliases', 'personality_traits', 'motivations', 'fears', 'goals', 
                              'distinctive_features', 'development_arc', 'history', 'participants',
                              'consequences', 'causes', 'rules', 'related_settings', 
                              'consistency_notes', 'related_characters', 'related_events', 
                              'related_settings', 'tags']:
                        values.append(self._serialize_json_field(value))
                    else:
                        values.append(value)
                
                values.append(entity_id)
                
                sql = f"UPDATE {table} SET {', '.join(set_clauses)} WHERE id = ?"
                cursor.execute(sql, values)
                conn.commit()
                
                return cursor.rowcount > 0
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to update entity: {str(e)}", "UPDATE")
    
    def delete_entity(self, table: str, entity_id: str) -> bool:
        """删除实体（软删除）"""
        return self.update_entity(table, entity_id, {"status": "deleted"})
    
    def search_entities(self, table: str, conditions: Dict[str, Any] = None, 
                       limit: int = 100, offset: int = 0) -> List[Dict[str, Any]]:
        """搜索实体"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                
                sql = f"SELECT * FROM {table}"
                values = []
                
                if conditions:
                    where_clauses = []
                    for key, value in conditions.items():
                        if isinstance(value, list):
                            placeholders = ",".join(["?" for _ in value])
                            where_clauses.append(f"{key} IN ({placeholders})")
                            values.extend(value)
                        else:
                            where_clauses.append(f"{key} = ?")
                            values.append(value)
                    
                    sql += " WHERE " + " AND ".join(where_clauses)
                
                sql += f" LIMIT {limit} OFFSET {offset}"
                
                cursor.execute(sql, values)
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to search entities: {str(e)}", "SEARCH")
    
    def execute_custom_query(self, sql: str, params: tuple = None) -> List[Dict[str, Any]]:
        """执行自定义查询"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute(sql, params or ())
                rows = cursor.fetchall()
                return [dict(row) for row in rows]
                
        except sqlite3.Error as e:
            raise DatabaseError(f"Failed to execute custom query: {str(e)}", "CUSTOM_QUERY")
